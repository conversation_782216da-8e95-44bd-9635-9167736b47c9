using FleetXQ.Application.Features.Telemetry.DTOs;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.AspNetCore.SignalR.Client;
using Xunit;
using FluentAssertions;

namespace FleetXQ.Api.Tests.Integration;

public class TelemetryHubIntegrationTests : SignalRIntegrationTestBase
{
    private const string TelemetryHubPath = "/hubs/telemetry";

    public TelemetryHubIntegrationTests(WebApplicationFactory<Program> factory) : base(factory)
    {
    }

    [Fact]
    public async Task Connection_WithValidToken_ShouldConnectSuccessfully()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var token = CreateTestJwtToken(userId, "testuser", "User");

        // Act
        var connection = await StartConnectionAsync(TelemetryHubPath, token);

        // Assert
        AssertConnectionState(connection, HubConnectionState.Connected);

        // Cleanup
        await StopConnectionAsync(connection);
        await connection.DisposeAsync();
    }

    [Fact]
    public async Task Connection_WithoutToken_ShouldFailToConnect()
    {
        // Act & Assert
        var connection = await CreateHubConnectionAsync(TelemetryHubPath);
        
        var exception = await Assert.ThrowsAsync<HttpRequestException>(async () =>
        {
            await connection.StartAsync();
        });

        // Should fail due to authentication requirement
        exception.Message.Should().Contain("401");

        await connection.DisposeAsync();
    }

    [Fact]
    public async Task SubscribeToVehicle_WithValidVehicleId_ShouldReceiveConfirmation()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var vehicleId = Guid.NewGuid();
        var token = CreateTestJwtToken(userId, "testuser", "User");
        var connection = await StartConnectionAsync(TelemetryHubPath, token);

        var confirmationCapture = new SignalRMethodCapture<object>();
        connection.On<object>("SubscriptionConfirmed", data => confirmationCapture.Capture("SubscriptionConfirmed", data));

        // Act
        await connection.InvokeAsync("SubscribeToVehicle", vehicleId);

        // Assert
        await Task.Delay(1000); // Give some time for the response
        confirmationCapture.CallCount.Should().Be(1);
        
        var confirmation = confirmationCapture.GetLastCall();
        confirmation.Should().NotBeNull();
        confirmation!.Data.Should().NotBeNull();

        // Cleanup
        await StopConnectionAsync(connection);
        await connection.DisposeAsync();
    }

    [Fact]
    public async Task SubscribeToVehicle_WithEmptyVehicleId_ShouldReceiveError()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var token = CreateTestJwtToken(userId, "testuser", "User");
        var connection = await StartConnectionAsync(TelemetryHubPath, token);

        var errorCapture = new SignalRMethodCapture<object>();
        connection.On<object>("Error", data => errorCapture.Capture("Error", data));

        // Act & Assert
        await Assert.ThrowsAsync<HubException>(async () =>
        {
            await connection.InvokeAsync("SubscribeToVehicle", Guid.Empty);
        });

        // Cleanup
        await StopConnectionAsync(connection);
        await connection.DisposeAsync();
    }

    [Fact]
    public async Task UnsubscribeFromVehicle_WithValidVehicleId_ShouldReceiveConfirmation()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var vehicleId = Guid.NewGuid();
        var token = CreateTestJwtToken(userId, "testuser", "User");
        var connection = await StartConnectionAsync(TelemetryHubPath, token);

        // First subscribe
        await connection.InvokeAsync("SubscribeToVehicle", vehicleId);
        await Task.Delay(500);

        var confirmationCapture = new SignalRMethodCapture<object>();
        connection.On<object>("UnsubscriptionConfirmed", data => confirmationCapture.Capture("UnsubscriptionConfirmed", data));

        // Act
        await connection.InvokeAsync("UnsubscribeFromVehicle", vehicleId);

        // Assert
        await Task.Delay(1000);
        confirmationCapture.CallCount.Should().Be(1);

        // Cleanup
        await StopConnectionAsync(connection);
        await connection.DisposeAsync();
    }

    [Fact]
    public async Task GetVehicleStatus_WithValidVehicleId_ShouldReceiveStatus()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var vehicleId = Guid.NewGuid();
        var token = CreateTestJwtToken(userId, "testuser", "User");
        var connection = await StartConnectionAsync(TelemetryHubPath, token);

        var statusCapture = new SignalRMethodCapture<object>();
        connection.On<object>("VehicleStatus", data => statusCapture.Capture("VehicleStatus", data));

        // Act
        await connection.InvokeAsync("GetVehicleStatus", vehicleId);

        // Assert
        await Task.Delay(1000);
        statusCapture.CallCount.Should().Be(1);
        
        var status = statusCapture.GetLastCall();
        status.Should().NotBeNull();
        status!.Data.Should().NotBeNull();

        // Cleanup
        await StopConnectionAsync(connection);
        await connection.DisposeAsync();
    }

    [Theory]
    [InlineData("Admin")]
    [InlineData("Manager")]
    [InlineData("Driver")]
    [InlineData("User")]
    public async Task Connection_WithDifferentRoles_ShouldAllowAccess(string role)
    {
        // Arrange
        var userId = Guid.NewGuid();
        var token = CreateTestJwtToken(userId, $"user_{role.ToLower()}", role);

        // Act
        var connection = await StartConnectionAsync(TelemetryHubPath, token);

        // Assert
        AssertConnectionState(connection, HubConnectionState.Connected);

        // Should be able to call hub methods
        var vehicleId = Guid.NewGuid();
        await connection.InvokeAsync("GetVehicleStatus", vehicleId);

        // Cleanup
        await StopConnectionAsync(connection);
        await connection.DisposeAsync();
    }

    [Fact]
    public async Task MultipleConnections_ShouldBeHandledCorrectly()
    {
        // Arrange
        var users = CreateTestUsers("Admin", "Manager", "Driver");
        var connections = new List<HubConnection>();

        try
        {
            // Act - Create multiple connections
            foreach (var (userId, token) in users)
            {
                var connection = await StartConnectionAsync(TelemetryHubPath, token);
                connections.Add(connection);
            }

            // Assert - All connections should be active
            foreach (var connection in connections)
            {
                AssertConnectionState(connection, HubConnectionState.Connected);
            }

            // Test that each connection can call methods independently
            var vehicleId = Guid.NewGuid();
            foreach (var connection in connections)
            {
                await connection.InvokeAsync("GetVehicleStatus", vehicleId);
            }
        }
        finally
        {
            // Cleanup
            foreach (var connection in connections)
            {
                await StopConnectionAsync(connection);
                await connection.DisposeAsync();
            }
        }
    }

    [Fact]
    public async Task Connection_WhenDisconnected_ShouldCleanupProperly()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var vehicleId = Guid.NewGuid();
        var token = CreateTestJwtToken(userId, "testuser", "User");
        var connection = await StartConnectionAsync(TelemetryHubPath, token);

        // Subscribe to a vehicle
        await connection.InvokeAsync("SubscribeToVehicle", vehicleId);
        await Task.Delay(500);

        // Act - Disconnect abruptly
        await connection.StopAsync();

        // Assert
        AssertConnectionState(connection, HubConnectionState.Disconnected);

        // Cleanup
        await connection.DisposeAsync();
    }

    [Fact]
    public async Task SubscribeToVehicle_MultipleUsers_ShouldReceiveUpdatesIndependently()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var users = CreateTestUsers("Manager", "Driver");
        var connections = new List<HubConnection>();
        var captures = new List<SignalRMethodCapture<object>>();

        try
        {
            // Create connections and subscribe to the same vehicle
            foreach (var (userId, token) in users)
            {
                var connection = await StartConnectionAsync(TelemetryHubPath, token);
                connections.Add(connection);

                var capture = new SignalRMethodCapture<object>();
                captures.Add(capture);
                
                connection.On<object>("SubscriptionConfirmed", data => capture.Capture("SubscriptionConfirmed", data));
                connection.On<object>("TelemetryUpdate", data => capture.Capture("TelemetryUpdate", data));

                await connection.InvokeAsync("SubscribeToVehicle", vehicleId);
            }

            // Wait for confirmations
            await Task.Delay(1000);

            // Assert - Both users should receive subscription confirmations
            foreach (var capture in captures)
            {
                capture.GetCalls().Should().Contain(call => call.MethodName == "SubscriptionConfirmed");
            }
        }
        finally
        {
            // Cleanup
            foreach (var connection in connections)
            {
                await StopConnectionAsync(connection);
                await connection.DisposeAsync();
            }
        }
    }
}
