using FleetXQ.Application.Interfaces;
using FleetXQ.Infrastructure.Services;
using FleetXQ.Application.Interfaces;
using FleetXQ.Infrastructure.Authentication;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;

namespace FleetXQ.Infrastructure;

/// <summary>
/// Extension methods for configuring infrastructure services
/// </summary>
public static class DependencyInjection
{
    /// <summary>
    /// Adds infrastructure services to the dependency injection container
    /// </summary>
    /// <param name="services">The service collection</param>
    /// <param name="configuration">The configuration</param>
    /// <returns>The service collection</returns>
    public static IServiceCollection AddInfrastructure(this IServiceCollection services, IConfiguration configuration)
    {
        // Authentication Services
        services.AddAuthenticationServices();

        // Database configuration will be added here
        // Repository registrations will be added here
        // External service integrations will be added here

        // SignalR Services
        services.AddSignalRServices();

        return services;
    }

    /// <summary>
    /// Adds authentication services to the dependency injection container
    /// </summary>
    /// <param name="services">The service collection</param>
    /// <returns>The service collection</returns>
    private static IServiceCollection AddAuthenticationServices(this IServiceCollection services)
    {
        services.AddScoped<ITokenService, TokenService>();
        services.AddScoped<IPasswordHashingService, PasswordHashingService>();
        services.AddScoped<IAuthorizationService, AuthorizationService>();
        services.AddScoped<IPasswordValidationService, PasswordValidationService>();
        services.AddScoped<IRateLimitingService, RateLimitingService>();

        // Add memory cache for rate limiting
        services.AddMemoryCache();

        return services;
    }

    /// <summary>
    /// Adds SignalR-related services to the dependency injection container
    /// </summary>
    /// <param name="services">The service collection</param>
    /// <returns>The service collection</returns>
    public static IServiceCollection AddSignalRServices(this IServiceCollection services)
    {
        // Register SignalR connection manager
        services.AddSingleton<ISignalRConnectionManager, SignalRConnectionManager>();

        // Register SignalR notification service
        services.AddScoped<ISignalRNotificationService, SignalRNotificationService>();

        // Register SignalR connection state service
        services.AddSingleton<SignalRConnectionStateService>();

        return services;
    }
}
